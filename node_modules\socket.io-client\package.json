{"name": "socket.io-client", "version": "1.7.4", "keywords": ["realtime", "framework", "websocket", "tcp", "events", "client"], "main": "./lib/index", "files": ["lib/", "dist/"], "dependencies": {"backo2": "1.0.2", "component-bind": "1.0.0", "component-emitter": "1.2.1", "debug": "2.3.3", "engine.io-client": "~1.8.4", "has-binary": "0.1.7", "indexof": "0.0.1", "object-component": "0.0.3", "parseuri": "0.0.5", "socket.io-parser": "2.3.1", "to-array": "0.1.4"}, "devDependencies": {"babel-core": "6.4.5", "babel-eslint": "4.1.7", "babel-loader": "6.2.1", "babel-preset-es2015": "6.3.13", "base64-arraybuffer": "0.1.5", "concat-stream": "1.5.1", "derequire": "2.0.3", "eslint-config-standard": "4.4.0", "eslint-plugin-standard": "1.3.1", "expect.js": "0.3.1", "gulp": "3.9.0", "gulp-eslint": "1.1.1", "gulp-file": "0.2.0", "gulp-istanbul": "0.10.3", "gulp-minify": "0.0.14", "gulp-mocha": "2.2.0", "gulp-task-listing": "1.0.1", "has-cors": "1.1.0", "imports-loader": "^0.6.5", "istanbul": "0.4.2", "mocha": "2.3.4", "socket.io": "1.7.4", "strip-loader": "0.1.2", "text-blob-builder": "0.0.1", "uglify-js": "2.6.1", "webpack-stream": "3.2.0", "zuul": "3.11.0", "zuul-builder-webpack": "1.1.0", "zuul-ngrok": "4.0.0"}, "scripts": {"test": "gulp test"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/Automattic/socket.io-client.git"}, "license": "MIT"}