<%- include('layout/header') %>
<h2 class="mb-4">Welcome to the Dashboard, <%= user.name %>!</h2>
<div class="row">
    <div class="col-md-6">
        <h3>Other Users</h3>
        <ul class="list-group">
            <% if (users && users.length > 0) { %>
                <% for (let i = 0; i < users.length; i++) { %>
                    <li class="list-group-item cursor-pointer user-list d-flex align-items-center" data-id="<%= users[i]._id %>">
                        <img src="<%= users[i].image %>"  
                             class="img-fluid rounded-circle me-2"
                             style="width: 50px; height: 50px;">
                        <span><%= users[i].name %></span>
                        <% if(users[i].is_online === '1'){ %>
                            <span class="badge bg-success ms-auto" id="<%= users[i]._id %>-status">Online</span>
                        <% } else { %>
                            <span class="badge bg-secondary ms-auto" id="<%= users[i]._id %>-status">Offline</span>
                        <% } %>
                    </li>  
                <% } %>
            <% } else { %>
                <li class="list-group-item">No other users found.</li>
            <% } %>
        </ul>
    </div>

    <div class="col-md-6">
        <h3 class="start-head">Click to start chat</h3>
        <div class="chat-section" style="display:none;">
            <div class="chat-container border p-2 mb-2" style="height:300px; overflow-y:auto;">
                <!-- messages will appear here -->
            </div>

            <form id="chat-form">
                <input type="text" class="form-control mb-2" id="message" placeholder="Type your message..." required>
                <input type="submit" class="btn btn-primary w-100" value="Send">
            </form>
        </div>
    </div>
</div>


<!-- Modal -->
<div class="modal fade" id="deleteChatModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle">Delete Chat</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form action="" id="deleteChatForm" method="post">
      <div class="modal-body">
        <input type="hidden" name="id" id="delete-chat-id" >
        <p>Are you sure you want to delete this chat?</p>
        <p><b id="delete-msg"></b></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="submit" class="btn btn-danger">Delete</button>
      </div>
      </form>
    </div>
  </div>
</div>
<!-- Modal -->
<div class="modal fade" id="updateChatModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle">Edit Chat</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form action="" id="updateChatForm" method="post">
      <div class="modal-body">
        <input type="hidden" name="id" id="edit-chat-id" >
        <input type="text" name="message" id="edit-message" class="form-control" required>
        
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="submit" class="btn btn-primary">Update</button>
      </div>
      </form>
    </div>
  </div>
</div>

<style>
.fa-trash {
    cursor: pointer;
    color: #ffffff;
    margin-left: 8px;
    padding: 2px;
    opacity: 0.7;
    font-size: 12px;
}
.fa-trash:hover {
    opacity: 1;
    color: #ffcccc;
}
.chat-container {
    padding: 15px !important;
}
.text-end {
    text-align: right !important;
}
.text-start {
    text-align: left !important;
}
.current-user-chat {
    margin-bottom: 10px;
}
.current-user-chat h4 {
    background-color: #007bff;
    color: white;
    padding: 8px 12px;
    border-radius: 18px;
    margin: 0;
    display: inline-flex;
    align-items: center;
    max-width: 70%;
    margin-left: auto;
    word-wrap: break-word;
}
.other-user-chat {
    margin-bottom: 10px;
}
.other-user-chat h4 {
    background-color: #f1f1f1;
    color: #333;
    padding: 8px 12px;
    border-radius: 18px;
    margin: 0;
    display: inline-block;
    max-width: 70%;
    word-wrap: break-word;
}
.message-content {
    flex: 1;
    margin-right: 8px;
}
</style>

<script>

    const sender_id = '<%= user._id %>';
    let receiver_id;

    // Socket.IO connection
    const socket = io('/user-namespace', { auth: { token: sender_id } });

    $(document).ready(function(){

        // User list click
        $(".user-list").click(function(){
            $(".start-head").hide();
            $(".chat-section").show();
            receiver_id = $(this).data("id");
            console.log("Requesting chat history for:", { sender_id, receiver_id });
            socket.emit("existsChat", { sender_id, receiver_id });
        });

        // Chat form submission
        $("#chat-form").submit(async function(e){
            e.preventDefault();
            const message = $("#message").val().trim();
            if(!message || !receiver_id) return;

            try {
                // Save to DB
                const response = await fetch('/saveChat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ sender_id, receiver_id, message })
                });
                const data = await response.json();

                if(data.success){
                    $("#message").val('');
                    $(".chat-container").append(`
                        <div class="current-user-chat text-end" id ="${data.data._id}">
                            <h4>
                                <span class="message-content">${data.data.message}</span>
                                <i class="fa fa-trash" aria-hidden="true" data-id="${data.data._id}" data-toggle="modal" data-target="#deleteChatModal"></i>
                                <i class="fa fa-edit" aria-hidden="true" data-id="${data.data._id}" data-toggle="modal" data-message="${data.data.message}" data-target="#updateChatModal"></i>
                                </h4>
                        </div>
                    `);
                    $(".chat-container").scrollTop($(".chat-container")[0].scrollHeight);

                    
                    socket.emit("newMessage", data.data);
                } else {
                    alert(data.msg);
                }
            } catch(err){
                console.error("Fetch error:", err);
            }
        });

        
        socket.on("loadNewChat", (chat) => {
            if(chat.sender_id === receiver_id && chat.receiver_id === sender_id){
                $(".chat-container").append(`
                    <div class="other-user-chat text-start" id="${chat._id}">
                        <h4>${chat.message}</h4>
                    </div>
                `);
                $(".chat-container").scrollTop($(".chat-container")[0].scrollHeight);
            }
        });

        // Update online/offline badges
        socket.on('getOnlineUser', data => {
            $("#" + data.user_id + "-status")
                .removeClass("bg-secondary")
                .addClass("bg-success")
                .text("Online");
        });

        socket.on('getOfflineUser', data => {
            $("#" + data.user_id + "-status")
                .removeClass("bg-success")
                .addClass("bg-secondary")
                .text("Offline");
        });
    });
    socket.on("loadChatHistory", (chats) => {
        console.log("Received chat history:", chats);
        $(".chat-container").html('');
        let html = '';
        for(let i = 0; i < chats.length; i++){
            let addClass = '';
            if(chats[i].sender_id === sender_id){
                addClass = 'current-user-chat text-end';
            } else {
                addClass = 'other-user-chat text-start';
            }
            if(chats[i].sender_id === sender_id){
                html += `<div class="${addClass}" id="${chats[i]._id}">
                    <h4>
                        <span class="message-content">${chats[i].message}</span>
                        <i class="fa fa-trash" aria-hidden="true" data-id="${chats[i]._id}" data-toggle="modal" data-target="#deleteChatModal"></i>
                        <i class="fa fa-edit" aria-hidden="true" data-id="${chats[i]._id}" data-toggle="modal" data-message="${chats[i].message}" data-target="#updateChatModal"></i>
                    </h4>
                </div>`;
            } else {
                html += `<div class="${addClass}" id="${chats[i]._id}">
                    <h4>${chats[i].message}</h4>
                </div>`;
            }
        }
        console.log("Generated HTML:", html);
        $(".chat-container").append(html);
        $(".chat-container").scrollTop($(".chat-container")[0].scrollHeight);
    });
    //delete chat
    $(document).on('click', '.fa-trash', function() {
        console.log('Trash icon clicked!');
        let msg = $(this).parent().text();
        $('#delete-msg').text(msg);
        $('#delete-chat-id').val($(this).attr('data-id'));

        // Manually show the modal (Bootstrap 4)
        $('#deleteChatModal').modal('show');
    });
    $('#deleteChatForm').submit(async function(e) {
        e.preventDefault();
        let chatId = $('#delete-chat-id').val();
        try {
            const response = await fetch('/deleteChat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id: chatId })
            });
            const data = await response.json();
            if(data.success){
                $('#' + chatId).remove();
                $('#deleteChatModal').modal('hide');
                socket.emit("chatDeleted", { chatId });
            } else {
                alert(data.msg);
            }
        } catch(err) {
            console.error("Fetch error:", err);
        }
        socket.on("chatDeleted", ({ chatId }) => {
            $('#' + chatId).remove();
        });
    });

    //update chat
    $(document).on('click', '.fa-edit', function() {
       $('#edit-chat-id').val($(this).attr('data-id'));
       $('#edit-message').val($(this).attr('data-message'));

    });
    $('#updateChatForm').submit(async function(e) {
        e.preventDefault();
        let chatId = $('#edit-chat-id').val();
        let message = $('#edit-message').val().trim();
        if(!message) return;
        try {
            const response = await fetch('/updateChat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id: chatId, message })
            });
            const data = await response.json();
            if(data.success){
                $('#' + chatId).find('.message-content').text(message);
                $('#updateChatModal').modal('hide');
                socket.emit("chatUpdated", { chatId, message });
            } else {
                alert(data.msg);
            }
        } catch(err) {
            console.error("Fetch error:", err);
        }
        socket.on("chatUpdated", ({ chatId, message }) => {
            $('#' + chatId).find('.message-content').text(message);
        });
    }); 
</script>

<%- include('layout/footer') %>
