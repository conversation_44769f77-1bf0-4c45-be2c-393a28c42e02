<%- include('layout/header') %>
<h2 class="mb-4">Welcome to the Dashboard, <%= user.name %>!</h2>
<div class="row">
    <div class="col-md-6">
        <h3>Other Users</h3>
        <ul class="list-group">
            <% if (users && users.length > 0) { %>
                <% for (let i = 0; i < users.length; i++) { %>
                    <li class="list-group-item cursor-pointer user-list d-flex align-items-center" data-id="<%= users[i]._id %>">
                        <img src="<%= users[i].image %>"  
                             class="img-fluid rounded-circle me-2"
                             style="width: 50px; height: 50px;">
                        <span><%= users[i].name %></span>
                        <% if(users[i].is_online === '1'){ %>
                            <span class="badge bg-success ms-auto" id="<%= users[i]._id %>-status">Online</span>
                        <% } else { %>
                            <span class="badge bg-secondary ms-auto" id="<%= users[i]._id %>-status">Offline</span>
                        <% } %>
                    </li>  
                <% } %>
            <% } else { %>
                <li class="list-group-item">No other users found.</li>
            <% } %>
        </ul>
    </div>

    <div class="col-md-6">
        <h3 class="start-head">Click to start chat</h3>
        <div class="chat-section" style="display:none;">
            <div class="chat-container border p-2 mb-2" style="height:300px; overflow-y:auto;">
                <!-- messages will appear here -->
            </div>

            <form id="chat-form">
                <input type="text" class="form-control mb-2" id="message" placeholder="Type your message..." required>
                <input type="submit" class="btn btn-primary w-100" value="Send">
            </form>
        </div>
    </div>
</div>


<!-- Modal -->
<div class="modal fade" id="deleteChatModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle">Delete Chat</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="" id="deleteChatForm" method="post">
      <div class="modal-body">
        <input type="hidden" name="id" id="delete-chat-id" >
        <p>Are you sure you want to delete this chat?</p>
        <p><b id="delete-msg"></b></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="submit" class="btn btn-danger">Delete</button>
      </div>
      </form>
    </div>
  </div>
</div>
<script>

    const sender_id = '<%= user._id %>';
    let receiver_id;

    // Socket.IO connection
    const socket = io('/user-namespace', { auth: { token: sender_id } });

    $(document).ready(function(){

        // User list click
        $(".user-list").click(function(){
            $(".start-head").hide();
            $(".chat-section").show();
            receiver_id = $(this).data("id");
            console.log("Requesting chat history for:", { sender_id, receiver_id });
            socket.emit("existsChat", { sender_id, receiver_id });
        });

        // Chat form submission
        $("#chat-form").submit(async function(e){
            e.preventDefault();
            const message = $("#message").val().trim();
            if(!message || !receiver_id) return;

            try {
                // Save to DB
                const response = await fetch('/saveChat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ sender_id, receiver_id, message })
                });
                const data = await response.json();

                if(data.success){
                    $("#message").val('');
                    $(".chat-container").append(`
                        <div class="current-user-chat text-end" id ="${data.data._id}">
                            <h4>${data.data.message}
                                <i class="fa fa-trash" aria-hidden="true" data-id="${data.data._id}" data-bs-toggle="modal" data-bs-target="#deleteChatModal"></i>
                                </h4>
                        </div>
                    `);
                    $(".chat-container").scrollTop($(".chat-container")[0].scrollHeight);

                    
                    socket.emit("newMessage", data.data);
                } else {
                    alert(data.msg);
                }
            } catch(err){
                console.error("Fetch error:", err);
            }
        });

        
        socket.on("loadNewChat", (chat) => {
            if(chat.sender_id === receiver_id && chat.receiver_id === sender_id){
                $(".chat-container").append(`
                    <div class="other-user-chat text-start" id="${chat._id}">
                        <h4>${chat.message}</h4>
                    </div>
                `);
                $(".chat-container").scrollTop($(".chat-container")[0].scrollHeight);
            }
        });

        // Update online/offline badges
        socket.on('getOnlineUser', data => {
            $("#" + data.user_id + "-status")
                .removeClass("bg-secondary")
                .addClass("bg-success")
                .text("Online");
        });

        socket.on('getOfflineUser', data => {
            $("#" + data.user_id + "-status")
                .removeClass("bg-success")
                .addClass("bg-secondary")
                .text("Offline");
        });
    });
    socket.on("loadChatHistory", (chats) => {
        console.log("Received chat history:", chats);
        $(".chat-container").html('');
        let html = '';
        for(let i = 0; i < chats.length; i++){
            let addClass = '';
            if(chats[i].sender_id === sender_id){
                addClass = 'current-user-chat text-end';
            } else {
                addClass = 'other-user-chat text-start';
            }
            html += `<div class="${addClass}" id="${chats[i]._id}"><h4>${chats[i].message}</h4>`;
                 if(chats[i].sender_id === sender_id){
                html += `<i class="fa fa-trash" aria-hidden="true" data-id="${chats[i]._id}" data-bs-toggle="modal" data-bs-target="#deleteChatModal"></i>`;
                 }
            html += `</div>`;
        }
        console.log("Generated HTML:", html);
        $(".chat-container").append(html);
        $(".chat-container").scrollTop($(".chat-container")[0].scrollHeight);
    });
    //delete chat
    $(document).on('click', '.fa-trash', function() {
        let msg = $(this).parent().text();
        $('#delete-msg').text(msg);
        $('#delete-chat-id').val($(this).attr('data-id'));
    });
    $('#deleteChatForm').submit(async function(e) {
        e.preventDefault();
        let chatId = $('#delete-chat-id').val();
        try {
            const response = await fetch('/deleteChat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id: chatId })
            });
            const data = await response.json();
            if(data.success){
                $('#' + chatId).remove();
                const modal = bootstrap.Modal.getInstance(document.getElementById('deleteChatModal'));
                modal.hide();
            } else {
                alert(data.msg);
            }
        } catch(err) {
            console.error("Fetch error:", err);
        }
    });
</script>

<%- include('layout/footer') %>
