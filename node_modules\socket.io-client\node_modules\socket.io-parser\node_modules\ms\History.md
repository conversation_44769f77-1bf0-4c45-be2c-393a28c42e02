
0.7.1 / 2015-04-20
==================

  * prevent extraordinary long inputs (@evilpacket)
  * Fixed broken readme link

0.7.0 / 2014-11-24
==================

 * add time abbreviations, updated tests and readme for the new units
 * fix example in the readme.
 * add LICENSE file

0.6.2 / 2013-12-05
==================

 * Adding repository section to package.json to suppress warning from NPM.

0.6.1 / 2013-05-10
==================

  * fix singularization [visionmedia]

0.6.0 / 2013-03-15
==================

  * fix minutes

0.5.1 / 2013-02-24
==================

  * add component namespace

0.5.0 / 2012-11-09
==================

  * add short formatting as default and .long option
  * add .license property to component.json
  * add version to component.json

0.4.0 / 2012-10-22
==================

  * add rounding to fix crazy decimals

0.3.0 / 2012-09-07
==================

  * fix `ms(<String>)` [visionmedia]

0.2.0 / 2012-09-03
==================

  * add component.json [visionmedia]
  * add days support [visionmedia]
  * add hours support [visionmedia]
  * add minutes support [visionmedia]
  * add seconds support [visionmedia]
  * add ms string support [visionmedia]
  * refactor tests to facilitate ms(number) [visionmedia]

0.1.0 / 2012-03-07
==================

  * Initial release
