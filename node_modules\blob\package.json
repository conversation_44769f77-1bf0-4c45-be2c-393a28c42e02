{"name": "blob", "description": "Abstracts out Blob and uses BlobBulder in cases where it is supported with any vendor prefix.", "version": "0.0.4", "homepage": "https://github.com/rase-/blob", "dependencies": {}, "devDependencies": {"mocha": "1.17.1", "expect.js": "0.2.0", "zuul": "1.5.4", "browserify": "3.30.1"}, "repository": {"type": "git", "url": "**************:rase-/blob.git"}, "scripts": {"test": "make test"}}