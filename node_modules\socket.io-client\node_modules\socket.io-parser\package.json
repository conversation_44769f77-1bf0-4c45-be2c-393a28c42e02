{"name": "socket.io-parser", "version": "2.3.1", "description": "socket.io protocol parser", "repository": {"type": "git", "url": "https://github.com/Automattic/socket.io-parser.git"}, "files": ["binary.js", "index.js", "is-buffer.js"], "dependencies": {"debug": "2.2.0", "json3": "3.3.2", "component-emitter": "1.1.2", "isarray": "0.0.1"}, "devDependencies": {"benchmark": "1.0.0", "expect.js": "0.2.0", "mocha": "1.16.2", "zuul": "3.11.0", "zuul-ngrok": "4.0.0"}, "scripts": {"test": "make test"}, "license": "MIT"}