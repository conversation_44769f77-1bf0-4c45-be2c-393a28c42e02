const express = require('express');
const user_route = express();
const bodyParser = require('body-parser');
user_route.use(bodyParser.json());
user_route.use(bodyParser.urlencoded({ extended: true }));
user_route.set('view engine', 'ejs');
user_route.set('views', './views');
user_route.use(express.static('public'));
const path = require('path');
const multer = require('multer');
const auth = require('../middlewares/auth'); 
const session = require('express-session');
const {SESSION_SECRET} = process.env;
user_route.use(session({
    secret: SESSION_SECRET || "defaultSecret",
    resave: false,
    saveUninitialized: true,
    cookie: { secure: false }
}));
const userController = require('../controllers/userController');


const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, path.join(__dirname, '../public/images'));
    },
    filename: function (req, file, cb) {
        const name = Date.now() + "-" + file.originalname;
        cb(null, name);
    }
});

const uploads = multer({ storage: storage });


user_route.get('/register',auth.isLogout,userController.registerLoad)
user_route.post('/register',uploads.single("image"),userController.register)

user_route.get('/',auth.isLogout,userController.loadLogin)
user_route.post('/',userController.login)
user_route.get('/logout',auth.isLogin,userController.logout)
user_route.post('/saveChat',auth.isLogin,userController.saveChat)
user_route.post('/deleteChat',auth.isLogin,userController.deleteChat)

user_route.get('/dashboard',auth.isLogin,userController.loadDashboard)
user_route.use((req, res) => {
    res.redirect('/');
});
module.exports = user_route;