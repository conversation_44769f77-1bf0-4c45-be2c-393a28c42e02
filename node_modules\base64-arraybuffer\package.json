{"name": "base64-arraybuffer", "description": "Encode/decode base64 data into ArrayBuffers", "version": "0.1.5", "homepage": "https://github.com/niklasvh/base64-arraybuffer", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://hertzen.com"}, "repository": {"type": "git", "url": "https://github.com/niklasvh/base64-arraybuffer"}, "bugs": {"url": "https://github.com/niklasvh/base64-arraybuffer/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/niklasvh/base64-arraybuffer/blob/master/LICENSE-MIT"}], "main": "lib/base64-arraybuffer", "engines": {"node": ">= 0.6.0"}, "scripts": {"test": "grunt nodeunit"}, "devDependencies": {"grunt": "^0.4.5", "grunt-cli": "^0.1.13", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-nodeunit": "^0.4.1", "grunt-contrib-watch": "^0.6.1"}, "keywords": []}